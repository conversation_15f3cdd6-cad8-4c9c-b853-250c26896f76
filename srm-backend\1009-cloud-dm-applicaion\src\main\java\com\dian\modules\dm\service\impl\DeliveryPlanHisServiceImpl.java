/**
 * Copyright (c) 2016-2019 九点科技 All rights reserved.
 *
 * http://www.9dyun.cn
 *
 * 版权所有，侵权必究！
 */
package com.dian.modules.dm.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dian.client.base.BaseClient;
import com.dian.common.exception.RRException;
import com.dian.common.utils.DateUtils;
import com.dian.common.validator.Assert;
import com.dian.common.validator.ValidatorUtils;
import com.dian.common.validator.group.AddGroup;
import com.dian.common.validator.group.UpdateGroup;
import com.dian.common.utils.BeanConverter;
import com.dian.enums.WhetherEnum;
import com.dian.modules.base.vo.VendorVO;
import com.dian.modules.dm.entity.DeliveryPlanEntity;
import com.dian.modules.enums.sys.Ent_EntTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.collections.CollectionUtils;
import cn.hutool.core.util.StrUtil;

import java.util.Date;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import org.slf4j.Logger;
import com.dian.common.log.TraceLoggerFactory;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import org.springframework.transaction.annotation.Transactional;
import io.seata.spring.annotation.GlobalTransactional;
import com.dian.common.utils.PageUtils;
import com.dian.common.utils.Query;
import com.dian.common.server.CommonService;
import com.dian.modules.dm.dao.DeliveryPlanHisDao;
import com.dian.modules.dm.entity.DeliveryPlanHisEntity;
import com.dian.modules.dm.service.DeliveryPlanHisService;
import com.dian.modules.dm.vo.DeliveryPlanHisExportVO;

/**
 * 送货计划明细服务实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-05 10:27:31
 */
@Service("deliveryPlanHisService")
public class DeliveryPlanHisServiceImpl extends ServiceImpl<DeliveryPlanHisDao, DeliveryPlanHisEntity> implements DeliveryPlanHisService {

    protected Logger logger = TraceLoggerFactory.getLogger(getClass());

    @Autowired
    public CommonService commonService;

    @Autowired
    public BaseClient baseClient;

    /**
     * 送货计划明细分页
     * @param params
     * @return
     */
    @Override
    public PageUtils queryPage(Map<String, Object> params) {

        IPage<DeliveryPlanHisEntity> page = this.page(new Query<DeliveryPlanHisEntity>().getPage(params),getQueryWrapper(params) );
        return new PageUtils(page);
    }

    /**
     *  送货计划明细新增
     * @param deliveryPlanHisEntity
     * @return
     */
    @Override
    //@Transactional(rollbackFor = Throwable.class) //单体事务控制
    @GlobalTransactional(rollbackFor = Exception.class)//微服务事务控制
    public boolean saveInfo(DeliveryPlanHisEntity deliveryPlanHisEntity) {

        //保存
        this.save(deliveryPlanHisEntity);

        return true;
    }

    /**
     *送货计划明细更新
     * @param deliveryPlanHisEntity
     * @return
     */
    @Override
    //@Transactional(rollbackFor = Throwable.class) //单体事务控制
    @GlobalTransactional(rollbackFor = Exception.class)//微服务事务控制
    public boolean updateInfo(DeliveryPlanHisEntity deliveryPlanHisEntity) {

        //修改状态校验
        this.updateCheck(deliveryPlanHisEntity.getId());

        //主表数据完整性校验
        this.paramsCheck(deliveryPlanHisEntity, UpdateGroup.class);

        //更新主表
        this.updateById(deliveryPlanHisEntity);


        return true;
    }

    /**
     *送货计划明细删除
     * @param ids
     * @return
     */
    @Override
    //@Transactional(rollbackFor = Throwable.class) //单体事务控制
    @GlobalTransactional(rollbackFor = Exception.class)//微服务事务控制
    public boolean deleteInfo(Long[] ids) {

        //删除状态校验
        this.deleteCheck(ids);

        //更新主表
        this.remove(new QueryWrapper<DeliveryPlanHisEntity>().in("id",ids));

        return true;
    }

    /**
     * 送货计划明细详情
     * @param id
     * @return
     */
    @Override
    public DeliveryPlanHisEntity getInfo(Long id) {
        DeliveryPlanHisEntity deliveryPlanHisEntity =getById(id);
        return deliveryPlanHisEntity;
    }

    /**
     * 送货计划明细审核
     * @param id
     * @return
     */
    @Override
    //@Transactional(rollbackFor = Throwable.class) //单体事务控制
    @GlobalTransactional(rollbackFor = Exception.class)//微服务事务控制
    public boolean checkInfo(Long id) {
        DeliveryPlanHisEntity deliveryPlanHisEntity =this.getById(id);
        checkCheck(deliveryPlanHisEntity);
        //deliveryPlanHisEntity.setOrderState(OrderHead_OrderStateEnum.AUDITED.getValue());
        //deliveryPlanHisEntity.setOrderDate(new Date());
        ///deliveryPlanHisEntity.setCheckUserId(commonService.getUserId());
        //deliveryPlanHisEntity.setCheckUserName(commonService.getUserName());
        return this.updateById(deliveryPlanHisEntity);
    }

    /**
     *送货计划明细当前页or全部导出
     * @param params
     * @return
     */
    @Override
    public List<DeliveryPlanHisExportVO> exportList(Map<String, Object> params) {
        List<DeliveryPlanHisEntity> list;
        if ("0".equals(params.get("exprotType"))) {
            list= this.page(new Query<DeliveryPlanHisEntity>().getPage(params),getQueryWrapper(params)).getRecords();
        }else{
            list= this.list(getQueryWrapper(params));
        }

        List<DeliveryPlanHisExportVO> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)) {
            resultList = BeanConverter.convertList(list, DeliveryPlanHisExportVO.class);
        }
        return resultList;
    }



    /***********************************************************************************************/
    /****************************************** 私有方法 ********************************************/
    /***********************************************************************************************/

    /**
     * 修改状态校验
     *
     * @param
     */
    private void updateCheck(Long id) {
        DeliveryPlanHisEntity deliveryPlanHisEntity =this.getById(id);
        //if(deliveryPlanHisEntity.getOrderState().equals(OrderHead_OrderStateEnum.AUDITED.getValue())){
        //    throw new RRException(String.format("已审核的XXX禁止修改"));
        //}
    }
    /**
     * 审核状态校验
     *
     * @param
     */
    private void checkCheck(DeliveryPlanHisEntity deliveryPlanHisEntity) {
        //if(deliveryPlanHisEntity.getOrderState().equals(OrderHead_OrderStateEnum.AUDITED.getValue())){
        //    throw new RRException(String.format("[%s] 此XXX已审核"));
        //}
    }

    /**
     * 删除状态校验
     *
     * @param ids
     */
    private void deleteCheck(Long[] ids) {
        for (Long id:ids) {
                DeliveryPlanHisEntity deliveryPlanHisEntity = this.getById(id);
        }
        //if(!saleOrderHeadEntity.getOrderState().equals(OrderHead_OrderStateEnum.WAITCHECK.getValue())){
        //    throw new RRException(String.format("只允许删除未审核的XXX"));
        //}
    }

    /**
     * 新增和修改参数校验
     *
     * @param record
     */
    private void paramsCheck(DeliveryPlanHisEntity record, Class<?> cls) {
        ValidatorUtils.validateEntity(record, cls);
    }
    /**
     * 获取查询条件
     *
     * @param
     */
    private LambdaQueryWrapper<DeliveryPlanHisEntity> getQueryWrapper(Map<String, Object> params) {
        Long tenantId = commonService.getTenantId();
        if (Ent_EntTypeEnum.VENDOR.getValue().equals(commonService.getTenantInfo().getEntType())){
            VendorVO vendorVo = baseClient.getVendorVoBySourceId(tenantId);
            Assert.isNull(vendorVo,"供应商不存在！");
            tenantId = vendorVo.getTenantId();
            params.put("vendorId",vendorVo.getSoureId());
        }
        params.put("tenantId",tenantId);
        LambdaQueryWrapper<DeliveryPlanHisEntity> queryWrapper=new LambdaQueryWrapper<>();
        if (!StrUtil.isEmptyIfStr(params.get("tenantId"))){
            queryWrapper.eq(DeliveryPlanHisEntity::getTenantId, Long.parseLong(params.get("tenantId").toString()));
        }
        if (!StrUtil.isEmptyIfStr(params.get("vendorId"))){
            queryWrapper.eq(DeliveryPlanHisEntity::getVendorId, Long.parseLong(params.get("vendorId").toString()));
        }
        if(!StrUtil.isEmptyIfStr(params.get("goods"))){
            queryWrapper.and(wrapper -> wrapper
                    .like(DeliveryPlanHisEntity::getGoodsErpCode,params.get("goods").toString().trim()).or()
                    .like(DeliveryPlanHisEntity::getGoodsName,params.get("goods").toString().trim()).or()
                    .like(DeliveryPlanHisEntity::getGoodsModel,params.get("goods").toString().trim())
            );
        }
        if (!StrUtil.isEmptyIfStr(params.get("vendor"))){
            queryWrapper.and(wrapper ->
                    wrapper.like(DeliveryPlanHisEntity::getVendorName, params.get("vendor").toString().trim())
                            .or()
                            .like(DeliveryPlanHisEntity::getVendorCode, params.get("vendor").toString().trim())
            );
        }
        if (!StrUtil.isEmptyIfStr(params.get("planNo"))){
            queryWrapper.eq(DeliveryPlanHisEntity::getPlanNo, params.get("planNo").toString());
        }
        try {
            if (params.get("queryDate") != null && !params.get("queryDate").equals("")) {
                String[] split = params.get("queryDate").toString().split(" 至 ");
                queryWrapper.between(DeliveryPlanHisEntity::getPlanDate, split[0] + " 00:00:00", split[1] + " 23:59:59");
            }
        } catch (Exception e) {
            logger.error("搜索日期有误");
            throw new RRException("日期输入有误");
        }
        //物料id
        if (!StrUtil.isEmptyIfStr(params.get("goodsId"))){
            queryWrapper.eq(DeliveryPlanHisEntity::getGoodsId,params.get("goodsId").toString().trim());
        }
        //机构id
        if(!StrUtil.isEmptyIfStr(params.get("deptId"))){
            queryWrapper.eq(DeliveryPlanHisEntity::getDeptId,params.get("deptId").toString().trim());
        }
        //计划要求送货日期
        if(!StrUtil.isEmptyIfStr(params.get("planDate"))){
            queryWrapper.eq(DeliveryPlanHisEntity::getPlanDate, params.get("planDate").toString());
        }
        //查询不为关闭
        if(!StrUtil.isEmptyIfStr(params.get("isClosed"))){
            queryWrapper.ne(DeliveryPlanHisEntity::getDeleteFlag, WhetherEnum.YES.getCode());
        }
        //采购员
        if(!StrUtil.isEmptyIfStr(params.get("purchaserId"))){
            queryWrapper.eq(DeliveryPlanHisEntity::getPurchaserId, Long.parseLong(params.get("purchaserId").toString()));
        }
        //采购组
        if(!StrUtil.isEmptyIfStr(params.get("purchasingGroup"))){
            queryWrapper.eq(DeliveryPlanHisEntity::getPurchasingGroup, params.get("purchasingGroup").toString());
        }
        //采购组
        if(!StrUtil.isEmptyIfStr(params.get("mrpRegion"))){
            queryWrapper.eq(DeliveryPlanHisEntity::getMrpRegion, params.get("mrpRegion").toString());
        }
        if (!StrUtil.isEmptyIfStr(params.get("orderType"))){
            queryWrapper.eq(DeliveryPlanHisEntity::getOrderType, Integer.parseInt(params.get("orderType").toString()));
        }
        queryWrapper.orderByDesc(DeliveryPlanHisEntity::getId);
        return queryWrapper;
    }
}
