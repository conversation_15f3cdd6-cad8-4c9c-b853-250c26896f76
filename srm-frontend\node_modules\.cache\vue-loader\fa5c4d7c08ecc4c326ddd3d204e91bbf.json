{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\dm\\deliveryPlan\\tenant\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\dm\\deliveryPlan\\tenant\\index.vue", "mtime": 1754560394608}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport store from \"@/store\";\r\nimport Download from \"./downloadDynaTemplate\";\r\nimport {\r\n  getDeliveryPlanList,\r\n  getDeliveryPlanItemInfo,\r\n  queryDeliveryPlanHisPage,\r\n} from \"@/api/dm/deliveryPlan\";\r\nimport dian from \"@/utils/dian\";\r\n\r\nexport default {\r\n  name: \"dm-deliveryPlan-tenant\",\r\n  components: {\r\n    Download\r\n  },\r\n  data() {\r\n    return {\r\n      PlanDeleteFlagOptions: store.getters.commonEnums['dm.DeliveryPlanDeleteFlagEnum'], // 订单类型\r\n      ValidTypeOptions:store.getters.commonEnums['comm.ValidEnum'],\r\n      queryParam: {\r\n        page: 1,\r\n        limit: 20,\r\n        planNo: '',\r\n        tenantIds:'tenantIds',//用于区分是否为供应商\r\n        orderNo : '', // 订单号\r\n        sortObj: 'dd.id DESC',//排序\r\n        whereType:1,//气泡查询条件 默认为1 - 全部\r\n        vendor : '', // 供应商编码|名称\r\n        goods : '', // 物料编码|名称|规格\r\n        keyword : '',  // 物料编码|名称|描述|图号\r\n        queryDate:'',//要求送货日期\r\n        startDate : '',  // 开始日期\r\n        orderType : '', // 订单类型\r\n        validType: '',\r\n        creater : '', // 创建人\r\n        deptId:'',//组织机构id\r\n        deleteFlag:'5',//排序方式(1-全部；2-按物料；3-按可制单数量>0)\r\n      },\r\n      planItemFromVisible:false,\r\n      queryDates:[],//要求送货日期\r\n      deliveryCount:{},//气泡数\r\n      listLoading: false,\r\n      btnLoading: false,\r\n      formVisible:false,\r\n      list: [],//列表数据\r\n      hisList: [],//历史列表数据\r\n      tabIdx: 1,\r\n      total: 0,//条数\r\n      hisTotal: 0,\r\n      selectedDatas: [],/*选择的数据*/\r\n      selectedNum: 0,/*选择数据的条数*/\r\n      userInfo:store.getters.userInfo,/*获取当前用户*/\r\n      dataForm:{\r\n        id:'',\r\n        saleNo:'',\r\n        deptName:'',\r\n        vendorName:'',\r\n        goodsErpCode:'',\r\n        goodsName:'',\r\n        goodsModel:'',\r\n        matchNum:'',\r\n        makeNum:'',\r\n        unCompetentNum:'',\r\n        refundNum:'',\r\n        planDate:'',\r\n      },\r\n      buttonFrom: {\r\n        count1: 'primary',\r\n        count2: '',\r\n        count3: '',\r\n        count4: '',\r\n        count5: '',\r\n        count6:'',\r\n      },\r\n    }\r\n  },\r\n  created() {\r\n    this.initData();\r\n  },\r\n  watch:{\r\n    $route:{\r\n      handler(to,from){\r\n        if(to.path === \"/dm/deliveryPlan/tenant\" && to.query){\r\n          this.queryParam.vendor = to.query.vendorCode;\r\n          if(to.query.planDate){\r\n            const planDate = new Date(to.query.planDate);\r\n            this.queryDates = [planDate, planDate];\r\n          }\r\n          this.initData();\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  methods: {\r\n    initData() {\r\n      if (this.tabIdx === 1){\r\n        this.listLoading = true;\r\n        if (this.queryDates.length !== 0) {\r\n          const startDate = this.$dian.dateFormat(this.queryDates[0], 'YYYY-MM-DD');\r\n          const endDate = this.$dian.dateFormat(this.queryDates[1], 'YYYY-MM-DD');\r\n          this.queryParam.queryDate = startDate +\" 至 \"+endDate;\r\n        }\r\n        let subCompanyInfoData = dian.storageGet('subCompanyInfo');\r\n        if (subCompanyInfoData){\r\n          this.queryParam.deptId = subCompanyInfoData.id;\r\n        }\r\n        getDeliveryPlanList(this.queryParam).then(res => {\r\n          this.total = res.data.totalCount;\r\n          this.list = res.data.list;\r\n          this.listLoading = false;\r\n        }).catch(() => {\r\n          this.listLoading = false;\r\n        })\r\n      }\r\n      if (this.tabIdx === 2){\r\n        this.initHisData()\r\n      }\r\n    },\r\n    // 历史数据\r\n    initHisData(){\r\n      this.listLoading = true;\r\n      if (this.queryDates.length !== 0) {\r\n        const startDate = this.$dian.dateFormat(this.queryDates[0], 'YYYY-MM-DD');\r\n        const endDate = this.$dian.dateFormat(this.queryDates[1], 'YYYY-MM-DD');\r\n        this.queryParam.queryDate = startDate +\" 至 \"+endDate;\r\n      }\r\n      let subCompanyInfoData = dian.storageGet('subCompanyInfo');\r\n      if (subCompanyInfoData){\r\n        this.queryParam.deptId = subCompanyInfoData.id;\r\n      }\r\n      console.log(this.queryParam)\r\n      queryDeliveryPlanHisPage(this.queryParam).then(res => {\r\n        this.total = res.data.totalCount;\r\n        this.hisList = res.data.list;\r\n        this.listLoading = false;\r\n      }).catch(() => {\r\n        this.listLoading = false;\r\n      })\r\n    },\r\n    //点击气泡查询\r\n    changeCountsButton(stat, name){\r\n      // 动态变换\r\n      this.buttonFrom.count1 = '';\r\n      this.buttonFrom.count2 = '';\r\n      this.buttonFrom[name] = 'primary';\r\n      if (stat === 1){\r\n        this.tabIdx = 1;\r\n        this.search();\r\n      }\r\n      if (stat === 2){\r\n        this.tabIdx = 2;\r\n        this.searchHis();\r\n      }\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.selectedDatas = selection.map(item => item)\r\n      //获取所有选中项数组的长度\r\n      this.selectedNum = selection.length\r\n    },\r\n    importHandle(){\r\n      this.search();\r\n    },\r\n    // 搜索方法，并返回到第一页\r\n    search() {\r\n      this.queryParam.page = 1;\r\n      this.initData();\r\n    },\r\n    searchHis() {\r\n      this.queryParam.page = 1;\r\n      this.initHisData();\r\n    },\r\n    // 重置方法\r\n    reset() {\r\n      this.queryParam = this.$options.data().queryParam;\r\n      this.queryDates = [];\r\n      this.search();\r\n    },\r\n    //打开送货单详情弹窗\r\n    openInfoForm(id){\r\n      this.dataForm = this.$options.data().dataForm;\r\n      this.planItemFromVisible = true;\r\n      if (id){\r\n        getDeliveryPlanItemInfo(id).then(res => {\r\n          this.dataForm = res.data\r\n        })\r\n      }\r\n    },\r\n    //关闭刷新列表数据\r\n    callDeliveryBoardList(){\r\n      this.formVisible = false;\r\n      this.search();\r\n    },\r\n    // 快速跳转至计划交期确认报表\r\n    openReplyReport() {\r\n      this.$router.push({path: '/dm/report/planReplyReport'})\r\n    },\r\n    //导出\r\n    exportHandle() {\r\n      this.$refs.export.init('/api/dm/deliveryPlanItem/export', '送货计划', this.queryParam);\r\n    },\r\n  }\r\n}\r\n", null]}